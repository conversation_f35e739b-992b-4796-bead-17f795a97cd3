import { NextRequest, NextResponse } from 'next/server'
import { authorizeApiRequest } from '@/app/(affiliate)/utils/authorizeApiRequest'
import { getAffiliateClickLogs, getAffiliateAnalytics } from '@/utilities/affiliate-tracking'

export async function GET(request: NextRequest) {
  try {
    const userRequest = await authorizeApiRequest()
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1') || 1
    const limit = parseInt(searchParams.get('limit') || '10') || 10
    const dateFrom = searchParams.get('dateFrom') ? new Date(searchParams.get('dateFrom')!) : undefined
    const dateTo = searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined
    const affiliateLinkId = searchParams.get('affiliateLinkId') ? parseInt(searchParams.get('affiliateLinkId')!) : undefined
    const analytics = searchParams.get('analytics') === 'true'

    if (analytics) {
      // Return analytics data
      const analyticsData = await getAffiliateAnalytics(userRequest.id, dateFrom, dateTo)
      
      return NextResponse.json({
        success: true,
        data: analyticsData
      })
    } else {
      // Return paginated click logs
      const clickLogs = await getAffiliateClickLogs(userRequest.id, {
        page,
        limit,
        dateFrom,
        dateTo,
        affiliateLinkId
      })

      // Format the response data
      const formattedData = clickLogs.docs.map((log: any) => ({
        id: log.id,
        sessionId: log.sessionId,
        ip: log.ip,
        location: log.location,
        referrer: log.referrer,
        userAgent: log.userAgent,
        affiliateLink: {
          id: log.affiliateLink?.id,
          affiliateCode: log.affiliateLink?.affiliateCode,
          event: log.affiliateLink?.event ? {
            id: log.affiliateLink.event.id,
            title: log.affiliateLink.event.title,
            slug: log.affiliateLink.event.slug
          } : null
        },
        deviceInfo: log.moreInformation?.deviceInfo,
        promoCode: log.moreInformation?.promoCode,
        utmParams: log.moreInformation?.utmParams,
        url: log.moreInformation?.url,
        createdAt: log.createdAt,
        updatedAt: log.updatedAt
      }))

      return NextResponse.json({
        success: true,
        data: formattedData,
        pagination: {
          page: clickLogs.page,
          limit: limit,
          totalPages: clickLogs.totalPages,
          totalDocs: clickLogs.totalDocs,
          hasNextPage: clickLogs.hasNextPage,
          hasPrevPage: clickLogs.hasPrevPage
        }
      })
    }

  } catch (error) {
    console.error('Error fetching affiliate click data:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error. Please try again later.'
      },
      { status: 500 }
    )
  }
}
